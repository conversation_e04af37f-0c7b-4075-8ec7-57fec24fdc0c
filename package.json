{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "homepage": "https://dotnetblogs.net/studytomy-react", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "predeploy": "npm run build", "deploy": "gh-pages -d dist"}, "dependencies": {"@microsoft/clarity": "^1.0.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-three/drei": "^9.121.5", "@react-three/fiber": "^8.17.14", "@supabase/supabase-js": "^2.50.0", "@types/react-helmet": "^6.1.11", "@types/react-slick": "^0.23.13", "@types/three": "^0.173.0", "framer-motion": "^11.11.17", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.22.3", "react-slick": "^0.30.2", "react-tsparticles": "^2.12.2", "slick-carousel": "^1.8.1", "swiper": "^11.1.14", "three": "^0.173.0", "three-mesh-bvh": "^0.8.0", "tsparticles": "^3.8.1", "tsparticles-slim": "^2.12.0", "typewriter-effect": "^2.21.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^22.8.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "gh-pages": "^6.2.0", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "terser": "^5.41.0", "typescript": "^5.7.3", "typescript-eslint": "^8.3.0", "vite": "^6.3.4"}}